<!DOCTYPE html>
<html>
<head>
    <title>Debug Alpine.js</title>
    <script>
        // Add a console.error listener to catch errors
        window.addEventListener('error', function(event) {
            console.log('Error caught:', event.message, 'at', event.filename, 'line', event.lineno, 'column', event.colno);
        });
        
        // Add a fetch listener to catch network requests
        const originalFetch = window.fetch;
        window.fetch = function() {
            console.log('Fetch called with:', arguments);
            return originalFetch.apply(this, arguments);
        };
        
        // Add an XMLHttpRequest listener
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function() {
            console.log('XHR open called with:', arguments);
            return originalXHROpen.apply(this, arguments);
        };
    </script>
</head>
<body>
    <h1>Alpine.js Debug</h1>
    <div id="app" x-data="{ message: 'Hello Alpine.js' }">
        <p x-text="message"></p>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js" defer></script>
    <script>
        // After Alpine.js loads
        document.addEventListener('alpine:init', () => {
            console.log('Alpine.js initialized');
            
            // Check if Alpine.js is properly loaded
            if (window.Alpine) {
                console.log('Alpine.js version:', window.Alpine.version);
                
                // Inspect Alpine.js methods
                console.log('Alpine.js methods:', Object.keys(window.Alpine));
            } else {
                console.error('Alpine.js not loaded properly');
            }
        });
    </script>
</body>
</html>
