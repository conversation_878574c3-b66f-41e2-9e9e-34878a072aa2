{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON><PERSON> Blog - Living in it, Living on it{% endblock %}

{% block head %}
{{ super() }}

{% include "includes/seo_meta.html" %}

<!-- Resource preloading -->
<link rel="dns-prefetch" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link rel="preload" href="/static/css/blog-combined.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
<noscript><link rel="stylesheet" href="/static/css/blog-combined.css"></noscript>

<!-- Scripts -->
<script src="/static/js/simple-html-fix.js"></script>
<script src="/static/js/alpine-init-helper.min.js"></script>
<script src="/static/js/alpine-component-functions.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.10.5/dist/cdn.min.js" defer></script>
<script src="/static/js/alpine-components-fixed.min.js" defer></script>
<script src="/static/js/defer-loading.min.js" defer></script>
<script src="/static/js/app.js" defer></script>

<!-- Critical CSS -->
<style>
/* Layout */
.blog-container {
    background-color: #FCF8D6;
    min-height: 100vh;
    padding: 1rem 0;
}

.blog-with-sidebar {
    display: grid;
    grid-template-columns: 250px 1fr 280px;
    gap: 2rem;
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 1rem;
    position: relative;
    align-items: start;
}

/* Sidebars */
.blog-sidebar-left {
    position: sticky;
    top: 2rem;
    height: fit-content;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(92, 14, 20, 0.3) transparent;
}

.blog-sidebar-left::-webkit-scrollbar {
    width: 4px;
}

.blog-sidebar-left::-webkit-scrollbar-track {
    background: transparent;
}

.blog-sidebar-left::-webkit-scrollbar-thumb {
    background: rgba(92, 14, 20, 0.3);
    border-radius: 2px;
}

.blog-sidebar-right {
    position: sticky;
    top: 2rem;
    height: fit-content;
    max-height: calc(100vh - 4rem);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(92, 14, 20, 0.3) transparent;
}

.blog-sidebar-right::-webkit-scrollbar {
    width: 4px;
}

.blog-sidebar-right::-webkit-scrollbar-track {
    background: transparent;
}

.blog-sidebar-right::-webkit-scrollbar-thumb {
    background: rgba(92, 14, 20, 0.3);
    border-radius: 2px;
}

/* Content */
.blog-main-content {
    width: 100%;
    min-width: 0;
    padding: 0 0.5rem;
}

.post-content img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem auto;
    border-radius: 0.375rem;
    width: auto !important;
    max-height: 300px;
    object-fit: contain;
}

.post-content * {
    max-width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    box-sizing: border-box;
}

.post-content pre, .post-content code {
    white-space: pre-wrap;
    overflow-x: auto;
}

/* Responsive Design */
@media (max-width: 1400px) {
    .blog-with-sidebar {
        grid-template-columns: 240px 1fr 270px;
        gap: 1.5rem;
        max-width: 1400px;
    }
}

@media (max-width: 1280px) {
    .blog-with-sidebar {
        grid-template-columns: 220px 1fr 250px;
        gap: 1.5rem;
        max-width: 1200px;
    }
}

@media (max-width: 1024px) {
    .blog-with-sidebar {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 1000px;
        padding: 0 0.5rem;
    }

    .blog-sidebar-left,
    .blog-sidebar-right {
        position: static;
        height: auto;
        max-height: none;
        overflow-y: visible;
        width: 100%;
        order: 2;
    }

    .blog-main-content {
        order: 1;
        padding: 0;
    }

    .blog-container {
        padding: 0.5rem 0;
    }
}

@media (max-width: 768px) {
    .blog-with-sidebar {
        gap: 1rem;
        max-width: 700px;
        padding: 0 0.25rem;
    }

    .blog-main-content {
        padding: 0;
    }

    .post-content img {
        max-height: 200px !important;
    }

    /* Stack sidebars on mobile */
    .blog-sidebar-left {
        order: 3;
    }

    .blog-sidebar-right {
        order: 2;
    }
}

@media (max-width: 640px) {
    .blog-container {
        padding: 0.25rem 0;
    }

    .blog-with-sidebar {
        padding: 0;
        gap: 0.75rem;
        max-width: 600px;
    }

    /* Improve mobile spacing */
    .bg-white.rounded-lg.shadow-md {
        margin-bottom: 1rem !important;
    }
}

/* Gallery styles */
.gallery-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}
.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    aspect-ratio: 1;
    cursor: pointer;
    transition: transform 0.3s ease;
}
.gallery-item:hover {
    transform: scale(1.05);
}
.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    will-change: transform;
}
.gallery-item-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(92, 14, 20, 0.7);
    color: white;
    padding: 0.5rem;
    font-size: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}
.gallery-item:hover .gallery-item-caption {
    opacity: 1;
}

/* Gallery modal */
.gallery-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}
.gallery-modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}
.gallery-modal-content img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
}
.gallery-modal-caption {
    color: white;
    text-align: center;
    padding: 1rem;
}
.gallery-modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    color: white;
    background: rgba(0,0,0,0.5);
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.gallery-modal-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: white;
    background: rgba(0,0,0,0.5);
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
.gallery-modal-prev {
    left: 1rem;
}
.gallery-modal-next {
    right: 1rem;
}

/* Church events/adverts styles */
.church-events {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    overflow: hidden;
    margin-bottom: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid rgba(92, 14, 20, 0.1);
    position: relative;
}
.church-events:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.15);
    border-color: rgba(92, 14, 20, 0.2);
}
.church-events-header {
    background: var(--primary-custom);
    background-image: linear-gradient(to right, var(--primary-custom), #8a1521);
    color: white;
    padding: 0.85rem 1rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
    border-bottom: 2px solid #f0e193;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.church-events-header span {
    position: relative;
    z-index: 2;
    padding-left: 0.25rem;
    font-size: 1.15rem;
    letter-spacing: 0.5px;
    color: #f0e193;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}
.church-events-header i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
    color: #f0e193;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.3);
}
.church-event-item {
    padding: 1.25rem;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.3s ease, transform 0.3s ease;
    position: relative;
}
.church-event-item:hover {
    background-color: #fcf8f8;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}
.church-event-item:last-child {
    border-bottom: none;
}
.church-event-date {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}
.church-event-date::before {
    content: '\f073';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.5rem;
    color: #999;
    font-size: 0.8rem;
}
.church-event-title {
    font-weight: bold;
    margin-bottom: 0.75rem;
    color: #5c0e14;
    font-size: 1.1rem;
    line-height: 1.3;
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(92, 14, 20, 0.1);
    transition: color 0.2s ease;
}
.church-event-item:hover .church-event-title {
    color: #8a1521;
}
.church-event-description {
    font-size: 0.95rem;
    color: #333;
    margin-bottom: 0.75rem;
    line-height: 1.5;
}
.church-event-link {
    font-size: 0.9rem;
    color: var(--primary-custom);
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    transition: color 0.2s ease;
    text-decoration: none;
    padding: 0.25rem 0;
}
.church-event-link:hover {
    color: #8a1521;
    text-decoration: underline;
}
.church-event-link i {
    margin-left: 0.5rem;
    transition: transform 0.2s ease;
}
.church-event-link:hover i {
    transform: translateX(3px);
}

/* Modal overlay styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: none;
    transition: opacity 0.3s ease;
}

.transition {
    transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
    transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-in {
    transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.duration-200 {
    transition-duration: 200ms;
}
.duration-300 {
    transition-duration: 300ms;
}
[x-cloak] {
    display: none !important;
}

/* Additional Layout Improvements */
.blog-sidebar-left .bg-white,
.blog-sidebar-right .bg-white {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.blog-sidebar-left .bg-white:hover,
.blog-sidebar-right .bg-white:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Improve main content spacing */
.blog-main-content h1 {
    margin-bottom: 1.5rem;
}

.blog-main-content article {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.blog-main-content article:hover {
    border-color: rgba(92, 14, 20, 0.1);
    transform: translateY(-2px);
}

/* Better button styling */
.bg-primary-custom {
    transition: all 0.2s ease;
}

.bg-primary-custom:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(92, 14, 20, 0.3);
}

/* Improve sidebar widget spacing */
.blog-sidebar-left > div,
.blog-sidebar-right > div {
    margin-bottom: 1.5rem;
}

.blog-sidebar-left > div:last-child,
.blog-sidebar-right > div:last-child {
    margin-bottom: 0;
}

/* Welcome section enhancements */
.welcome-intro-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.welcome-intro-card:hover {
    transform: translateY(-3px);
    border-color: rgba(92, 14, 20, 0.1);
}

.welcome-intro-card .fas {
    transition: transform 0.2s ease;
}

.welcome-intro-card:hover .fas {
    transform: scale(1.1);
}

/* Responsive adjustments for welcome section */
@media (max-width: 768px) {
    .welcome-intro-card {
        margin-bottom: 1rem;
    }

    .welcome-intro-card h3 {
        font-size: 1rem;
    }

    .welcome-intro-card p {
        font-size: 0.875rem;
    }
}

@media (max-width: 640px) {
    .bg-gradient-to-br {
        padding: 1.5rem !important;
    }

    .grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="blog-container blog-page">
    <!-- Main blog layout -->
    <div class="container mx-auto">
        <div class="blog-with-sidebar">
        <!-- Left Sidebar -->
        <div class="blog-sidebar-left">
            {% include "includes/daily_scripture.html" %}
            {% include "includes/categories_widget.html" %}
        </div>

        <!-- Main Content -->
        <main class="blog-main-content">
            {% include "includes/blog_header.html" %}

            {% include "includes/welcome_section.html" %}

            <!-- Blog Posts -->
            <div class="space-y-6">
                {% for post in posts %}
                <article class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
                    <h2 class="text-2xl font-bold mb-2">
                        <a href="{{ url_for('post_detail', post_id=post.id) }}" class="text-primary-custom hover:underline">
                            {{ post.title }}
                        </a>
                    </h2>
                    <div class="text-gray-600 text-sm mb-3 flex items-center space-x-4">
                        <span>
                            <i class="fas fa-user mr-1"></i>
                            By {{ post.author.username if post.author else 'Anonymous' }}
                        </span>
                        <span>
                            <i class="fas fa-calendar mr-1"></i>
                            {{ post.created_at.strftime('%B %d, %Y') if post.created_at else 'Unknown date' }}
                        </span>
                        {% if post.category %}
                        <span class="bg-gray-100 px-2 py-1 rounded text-xs">
                            <i class="fas fa-tag mr-1"></i>{{ post.category }}
                        </span>
                        {% endif %}
                    </div>
                    <div class="post-content mb-4 text-gray-700 leading-relaxed">
                        {{ post.content|striptags|truncate(300) }}
                    </div>
                    <div class="flex justify-between items-center">
                        <a href="{{ url_for('post_detail', post_id=post.id) }}" class="text-primary-custom font-semibold hover:underline">
                            Read More <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                        <div class="text-gray-500 text-sm">
                            <i class="fas fa-eye mr-1"></i>{{ post.views|default(0) }} views
                        </div>
                    </div>
                </article>
                {% else %}
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8 text-center">
                    <div class="text-6xl mb-4">📝</div>
                    <h3 class="text-xl font-semibold text-yellow-800 mb-2">No Blog Posts Yet</h3>
                    <p class="text-yellow-700 mb-4">Be the first to share your faith journey with our community!</p>
                    {% if is_authenticated %}
                    <a href="/create-post" class="bg-primary-custom hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-semibold">
                        <i class="fas fa-plus mr-2"></i>Create Your First Post
                    </a>
                    {% else %}
                    <a href="/register" class="bg-primary-custom hover:bg-primary-dark text-white px-6 py-3 rounded-lg font-semibold">
                        <i class="fas fa-user-plus mr-2"></i>Join Us to Start Writing
                    </a>
                    {% endif %}
                </div>
                {% endfor %}
            </div>

            <!-- Pagination (if needed) -->
            {% if posts|length >= 10 %}
            <div class="mt-8 flex justify-center">
                <nav class="flex space-x-2">
                    <a href="#" class="px-3 py-2 bg-gray-200 text-gray-600 rounded hover:bg-gray-300">Previous</a>
                    <a href="#" class="px-3 py-2 bg-primary-custom text-white rounded">1</a>
                    <a href="#" class="px-3 py-2 bg-gray-200 text-gray-600 rounded hover:bg-gray-300">2</a>
                    <a href="#" class="px-3 py-2 bg-gray-200 text-gray-600 rounded hover:bg-gray-300">Next</a>
                </nav>
            </div>
            {% endif %}
        </main>

        <!-- Right Sidebar -->
        <div class="blog-sidebar-right">
            {% include "includes/church_events_widget.html" %}
            {% include "includes/prayer_requests_widget.html" %}
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
{% include "includes/blog_footer.html" %}
{% endblock %}

<!-- Emergency close button for modals -->
<div id="modal-emergency-close" class="church-events-modal-emergency-close" style="display: none;" onclick="closeAllModals()">
    <i class="fas fa-times"></i> Close Modal
</div>

<!-- Break Reminder Modal -->
<div x-data="{
    showBreakReminder: false,
    breakReminderInterval: 60 * 60 * 1000,
    breakReminderTimer: null,
    lastBreakTime: null,
    init() {
        console.log('Break reminder component initialized');
        this.initBreakReminder();
    },
    initBreakReminder() {
        const storedLastBreakTime = localStorage.getItem('lastBreakTime');
        if (storedLastBreakTime) {
            this.lastBreakTime = parseInt(storedLastBreakTime);
            const timeElapsed = Date.now() - this.lastBreakTime;
            if (timeElapsed < this.breakReminderInterval) {
                const remainingTime = this.breakReminderInterval - timeElapsed;
                this.breakReminderTimer = setTimeout(() => {
                    this.showBreakReminder = true;
                }, remainingTime);
            } else {
                this.showBreakReminder = true;
            }
        } else {
            this.breakReminderTimer = setTimeout(() => {
                this.showBreakReminder = true;
            }, this.breakReminderInterval);
        }
    },
    takeBreak() {
        this.showBreakReminder = false;
        this.lastBreakTime = Date.now();
        localStorage.setItem('lastBreakTime', this.lastBreakTime);
        clearTimeout(this.breakReminderTimer);
        this.breakReminderTimer = setTimeout(() => {
            this.showBreakReminder = true;
        }, this.breakReminderInterval);
    },
    continueReading() {
        this.showBreakReminder = false;
        clearTimeout(this.breakReminderTimer);
        this.breakReminderTimer = setTimeout(() => {
            this.showBreakReminder = true;
        }, 15 * 60 * 1000);
    }
}">
    <!-- Break reminder overlay -->
    <div
        class="break-reminder-overlay"
        x-show="showBreakReminder"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100"
        x-transition:leave-end="opacity-0"
    ></div>
    <!-- Break reminder modal -->
    <div
        class="break-reminder"
        x-show="showBreakReminder"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95"
        x-transition:enter-end="opacity-100 transform scale-100"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100"
        x-transition:leave-end="opacity-0 transform scale-95"
    >
        <div class="text-center">
            <div class="flex justify-center mb-4">
                <i class="fas fa-coffee text-4xl text-primary-custom"></i>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-800">Time for a Break</h3>
            <p class="text-gray-600 mb-4">You've been reading for an hour now. Taking regular breaks is good for your spiritual and physical well-being.</p>
            <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 justify-center">
                <button
                    @click="takeBreak()"
                    class="bg-primary-custom hover:bg-primary-dark text-white px-4 py-2 rounded font-medium"
                >
                    <i class="fas fa-check mr-2"></i>Take a Break
                </button>
                <button
                    @click="continueReading()"
                    class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded font-medium"
                >
                    <i class="fas fa-book-reader mr-2"></i>Continue Reading
                </button>
            </div>
            <p class="text-xs text-gray-500 mt-4">"Come to me, all you who are weary and burdened, and I will give you rest." - Matthew 11:28</p>
        </div>
    </div>
</div>

<!-- Additional JavaScript for blog functionality -->
<script>
// Break reminder styles
document.addEventListener('DOMContentLoaded', function() {
    // Add break reminder styles if not already present
    if (!document.getElementById('break-reminder-styles')) {
        const style = document.createElement('style');
        style.id = 'break-reminder-styles';
        style.textContent = `
            .break-reminder-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 9998;
            }

            .break-reminder {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                border-radius: 0.5rem;
                padding: 2rem;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                z-index: 9999;
                max-width: 90%;
                width: 400px;
            }

            .church-events-modal-emergency-close {
                position: fixed;
                top: 1rem;
                right: 1rem;
                background: var(--primary-custom);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 0.25rem;
                cursor: pointer;
                z-index: 10000;
                font-size: 0.875rem;
                font-weight: 500;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }

            .church-events-modal-emergency-close:hover {
                background: var(--primary-dark);
            }
        `;
        document.head.appendChild(style);
    }
});

// Modal close functionality
function closeAllModals() {
    // Close any open modals
    const modals = document.querySelectorAll('[x-show]');
    modals.forEach(modal => {
        if (modal.__x && modal.__x.$data) {
            // Close Alpine.js modals
            Object.keys(modal.__x.$data).forEach(key => {
                if (key.includes('show') || key.includes('open') || key.includes('modal')) {
                    modal.__x.$data[key] = false;
                }
            });
        }
    });

    // Hide emergency close button
    const emergencyClose = document.getElementById('modal-emergency-close');
    if (emergencyClose) {
        emergencyClose.style.display = 'none';
    }
}

// Gallery functionality
function initializeGallery() {
    const galleryItems = document.querySelectorAll('.gallery-item');
    let currentImageIndex = 0;
    let galleryImages = [];

    galleryItems.forEach((item, index) => {
        const img = item.querySelector('img');
        if (img) {
            galleryImages.push({
                src: img.src,
                alt: img.alt || '',
                caption: item.querySelector('.gallery-item-caption')?.textContent || ''
            });

            item.addEventListener('click', () => {
                currentImageIndex = index;
                openGalleryModal();
            });
        }
    });

    function openGalleryModal() {
        const modal = document.createElement('div');
        modal.className = 'gallery-modal';
        modal.innerHTML = `
            <div class="gallery-modal-content">
                <div class="gallery-modal-close" onclick="closeGalleryModal()">&times;</div>
                <div class="gallery-modal-prev gallery-modal-nav" onclick="previousImage()">&lt;</div>
                <div class="gallery-modal-next gallery-modal-nav" onclick="nextImage()">&gt;</div>
                <img src="${galleryImages[currentImageIndex].src}" alt="${galleryImages[currentImageIndex].alt}">
                <div class="gallery-modal-caption">${galleryImages[currentImageIndex].caption}</div>
            </div>
        `;
        document.body.appendChild(modal);

        // Add event listeners
        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeGalleryModal();
        });

        document.addEventListener('keydown', handleGalleryKeydown);
    }

    window.closeGalleryModal = function() {
        const modal = document.querySelector('.gallery-modal');
        if (modal) {
            modal.remove();
            document.removeEventListener('keydown', handleGalleryKeydown);
        }
    };

    window.previousImage = function() {
        currentImageIndex = (currentImageIndex - 1 + galleryImages.length) % galleryImages.length;
        updateGalleryImage();
    };

    window.nextImage = function() {
        currentImageIndex = (currentImageIndex + 1) % galleryImages.length;
        updateGalleryImage();
    };

    function updateGalleryImage() {
        const modal = document.querySelector('.gallery-modal');
        if (modal) {
            const img = modal.querySelector('img');
            const caption = modal.querySelector('.gallery-modal-caption');
            img.src = galleryImages[currentImageIndex].src;
            img.alt = galleryImages[currentImageIndex].alt;
            caption.textContent = galleryImages[currentImageIndex].caption;
        }
    }

    function handleGalleryKeydown(e) {
        switch(e.key) {
            case 'Escape':
                closeGalleryModal();
                break;
            case 'ArrowLeft':
                previousImage();
                break;
            case 'ArrowRight':
                nextImage();
                break;
        }
    }
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeGallery);

// Smooth scrolling for anchor links
document.addEventListener('DOMContentLoaded', function() {
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});

// Lazy loading for images
document.addEventListener('DOMContentLoaded', function() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        });

        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => imageObserver.observe(img));
    }
});
</script>

{% block scripts %}
<!-- Additional page-specific scripts can be added here -->
{% endblock %}
