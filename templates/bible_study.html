{% extends "base.html" %}

{% block styles %}
<!-- Override any base styles that might cause transparency -->
<style>
    body, html, .container, #content, main, .content {
        background-color: #FCF8D6 !important;
        background-image: none !important;
        opacity: 1 !important;
    }
</style>
{% endblock %}

{% block head %}
{% include "includes/seo_meta.html" %}
<!-- Include Bible Study specific CSS -->
<link rel="stylesheet" href="/static/css/bible-study.css">
<style>
    .bible-verse {
        background-color: var(--secondary-custom);
        color: var(--text-on-secondary);
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
        position: relative;
    }

    .bible-verse-text {
        font-size: 1.25rem;
        line-height: 1.75;
        margin-bottom: 1rem;

    }

    .bible-verse-reference {
        font-weight: 600;
        text-align: right;
    }

    .bible-verse-translation {
        font-size: 0.875rem;
        opacity: 0.8;
    }

    .bible-hub-link {
        position: absolute;
        top: 1rem;
        right: 1rem;
        color: var(--primary-custom);
    }

    .bible-hub-link:hover {
        text-decoration: underline;
    }

    .search-container {
        margin-bottom: 2rem;
        background-color: white !important;
        padding: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 5;
    }

    .search-input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #e2e8f0;
        border-radius: 0.375rem;
        font-size: 1rem;
    }

    .search-button {
        background-color: var(--primary-custom);
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .search-button:hover {
        background-color: var(--primary-dark);
    }

    .popular-searches {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .popular-search {
        background-color: #f1f5f9;
        color: var(--primary-custom);
        border: none;
        border-radius: 9999px;
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .popular-search:hover {
        background-color: #e2e8f0;
    }

    .verse-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }

    .verse-card {
        background-color: white !important;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        border-left: 4px solid var(--primary-custom);
        transition: transform 0.2s, box-shadow 0.2s;
        position: relative;
        z-index: 5;
    }

    .verse-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .verse-card-text {
        font-size: 1rem;
        line-height: 1.5;
        margin-bottom: 1rem;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .verse-card-reference {
        font-weight: 600;
        color: var(--primary-custom);
    }

    .verse-card-translation {
        font-size: 0.75rem;
        color: #64748b;
    }

    .verse-card-link {
        display: inline-block;
        margin-top: 0.5rem;
        color: var(--primary-custom);
        font-size: 0.875rem;
    }

    .verse-card-link:hover {
        text-decoration: underline;
    }

    .daily-verse {
        background-color: var(--primary-custom) !important;
        color: white !important;
        padding: 2rem;
        border-radius: 0.5rem;
        margin-bottom: 2rem;
        position: relative;
        z-index: 5;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .daily-verse-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: black !important;
    }

    .daily-verse-text {
        font-size: 1.25rem;
        line-height: 1.75;
        margin-bottom: 1rem;
        color: black !important;
    }

    .daily-verse-reference {
        font-weight: 600;
        text-align: right;
        color: black !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- Background overlay to prevent transparency -->
<div class="background-overlay"></div>

<!-- Add x-cloak style to prevent flashing -->
<style>
    [x-cloak] { display: none !important; }
</style>

<div class="bible-study-container container mx-auto px-4 relative z-10">


<!-- Flag Content Modal -->
<div
    x-show="showFlagModal"
    x-cloak
    class="fixed inset-0 z-50 overflow-y-auto"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
>
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
    </div>
</div>

<div class="bible-study-content max-w-4xl mx-auto p-6 rounded-lg" x-data="bibleStudy()" x-init="init()">
    <!-- Video Embed Modal - Simplified and with x-cloak to prevent flashing -->
    <div
        x-show="showVideoEmbedModal"
        class="fixed inset-0 z-50 overflow-y-auto"
        x-cloak
        @keydown.escape.window="showVideoEmbedModal = false"
    >
        <div class="flex items-center justify-center min-h-screen px-4">
            <!-- Backdrop -->
            <div
                class="fixed inset-0 transition-opacity"
                @click="showVideoEmbedModal = false"
            >
                <div class="absolute inset-0 bg-black opacity-50"></div>
            </div>

            <!-- Modal content -->
            <div
                class="bg-white rounded-lg overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full"
                @click.away="showVideoEmbedModal = false"
            >
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Embed Video</h3>

                    <div class="mb-4">
                        <label for="video-url" class="block text-sm font-medium text-gray-700 mb-1">Video URL</label>
                        <input
                            type="text"
                            id="video-url"
                            x-model="videoUrl"
                            placeholder="Enter YouTube or Vimeo URL"
                            class="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary-custom"
                        >
                        <p class="text-xs text-gray-500 mt-1">Supported: YouTube and Vimeo</p>
                    </div>

                    <div class="flex justify-end">
                        <button
                            @click="showVideoEmbedModal = false"
                            class="mr-2 px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-100"
                        >
                            Cancel
                        </button>
                        <button
                            @click="addVideo()"
                            class="px-4 py-2 bg-primary-custom text-white rounded hover:bg-primary-dark"
                        >
                            Add Video
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-between align-items-center mb-6">
        <h1 class="text-3xl font-bold">Bible Study</h1>
        <div>
            <a href="/bible-study-posts" class="btn btn-secondary-custom me-2">
                <i class="fas fa-comments me-2"></i>Discussion Board
            </a>
            <a href="/chat/2" class="btn btn-primary-custom">
                <i class="fas fa-comment-dots me-2"></i>Chat Room
            </a>
        </div>
    </div>

    <!-- Daily Verse -->
    <div class="daily-verse" style="color: black !important;">
        <div class="daily-verse-title" style="color: black !important;">Verse of the Day</div>
        <div class="daily-verse-text" x-text="dailyVerse.text" style="color: black !important;"></div>
        <div class="daily-verse-reference" style="color: black !important;">
            <span x-text="dailyVerse.reference"></span>
            <a :href="'https://biblehub.com/' + dailyVerse.reference.toLowerCase().replace(/\s+/g, '').replace(':', '/') + '.htm'"
               target="_blank"
               class="ml-2 text-black underline" style="color: black !important;">
                View on BibleHub.com
            </a>
        </div>
        <!-- Debug info -->
        <div x-show="!dailyVerse.text" class="mt-2 p-2 bg-red-100 text-red-800 rounded">
            <p>Debug: Daily verse not loaded properly.</p>
            <p x-text="'Daily verse object: ' + JSON.stringify(dailyVerse)"></p>
        </div>
    </div>

    <!-- Search -->
    <div class="search-container verse-list-container">
        <h2 class="text-2xl font-bold mb-4">Search the Bible</h2>
        <div class="flex">
            <label for="bible-search-input" class="sr-only">Search the Bible</label>
            <input
                type="text"
                id="bible-search-input"
                name="bible-search"
                x-model="searchQuery"
                @keydown.enter="searchBible"
                placeholder="Enter a keyword, phrase, or reference (e.g., 'love', 'faith', 'John 3:16')"
                class="search-input flex-1 mr-2 p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-custom"
                autocomplete="off"
                style="min-height: 42px;"
            >
            <button @click="searchBible" class="search-button px-4 py-2 bg-primary-custom text-white rounded hover:bg-primary-dark transition-colors" style="background-color: #5c0e14 !important;">
                <i class="fas fa-search mr-2"></i> Search
            </button>
        </div>
        <div class="popular-searches mt-3">
            <span class="text-sm text-gray-600 mr-2">Popular searches:</span>
            <button @click="searchQuery = 'love'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">love</button>
            <button @click="searchQuery = 'faith'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">faith</button>
            <button @click="searchQuery = 'hope'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">hope</button>
            <button @click="searchQuery = 'peace'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">peace</button>
            <button @click="searchQuery = 'joy'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">joy</button>
            <button @click="searchQuery = 'John 3:16'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">John 3:16</button>
            <button @click="searchQuery = 'Psalm 23'; searchBible()" class="popular-search px-2 py-1 bg-gray-200 text-gray-800 rounded hover:bg-gray-300 transition-colors mr-2 mb-2 inline-block">Psalm 23</button>
        </div>
    </div>

    <!-- Search Results -->
    <div x-show="searchResults.length > 0" class="verse-list-container">
        <h2 class="text-2xl font-bold mb-4">Search Results</h2>
        <div class="verse-list">
            <template x-for="verse in searchResults" :key="verse.reference">
                <div class="verse-card">
                    <div class="verse-card-text" x-text="verse.text"></div>
                    <div class="verse-card-reference" x-text="verse.reference"></div>
                    <div class="verse-card-translation" x-text="verse.translation"></div>
                    <div class="flex justify-between mt-2">
                        <a :href="verse.biblehub_link" target="_blank" class="verse-card-link">
                            <i class="fas fa-external-link-alt mr-1"></i> View on BibleHub.com
                        </a>
                        <div class="flex space-x-2">
                            <button
                                @click="addNote(verse)"
                                class="text-sm bg-gray-200 hover:bg-gray-300 text-gray-800 px-2 py-1 rounded flex items-center"
                            >
                                <i class="fas fa-sticky-note mr-1"></i> Add Note
                            </button>

                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Featured Verses Button -->
    <div class="flex justify-center my-8">
        <button
            @click="showFeaturedVerses = !showFeaturedVerses"
            class="px-6 py-4 bg-primary-custom text-white font-semibold rounded-lg hover:bg-primary-dark transition duration-200 flex items-center shadow-md hover:shadow-lg transform hover:scale-105"
            style="background-color: #5c0e14 !important;"
        >
            <i class="fas fa-bible mr-2 text-xl"></i>
            <span x-text="showFeaturedVerses ? 'Hide Featured Verses' : 'Show Featured Verses'" class="text-lg">Show Featured Verses</span>
        </button>
    </div>

    <!-- Featured Verses -->
    <div class="verse-list-container text-center"
         x-show="showFeaturedVerses"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        <h2 class="text-2xl font-bold mb-4 text-center">Featured Verses</h2>
        <div class="verse-list">
            <template x-for="verse in featuredVerses" :key="verse.reference">
                <div class="verse-card">
                    <div class="verse-card-text" x-text="verse.text"></div>
                    <div class="verse-card-reference" x-text="verse.reference"></div>
                    <div class="verse-card-translation" x-text="verse.translation"></div>
                    <a :href="'https://biblehub.com/' + verse.reference.toLowerCase().replace(/\s+/g, '').replace(':', '/') + '.htm'"
                       target="_blank"
                       class="verse-card-link">
                        <i class="fas fa-external-link-alt mr-1"></i> View on BibleHub.com
                    </a>
                </div>
            </template>
        </div>
    </div>

    <!-- My Notes -->
    <div class="notes-container" x-show="notes.length > 0 || showNoteEditor">
        <h2 class="text-2xl font-bold mb-4">My Notes</h2>

        <!-- Note Editor -->
        <div x-show="showNoteEditor" class="note-editor">
            <h3 class="text-xl font-bold mb-4" x-text="currentNote.isEditing ? 'Edit Note' : 'Add Note'"></h3>

            <!-- Verse reference -->
            <div class="mb-4 p-3 bg-gray-50 rounded border-l-4 border-primary-custom" x-show="currentNote.verse !== null">
                <p x-text="currentNote.verse ? currentNote.verse.text : ''" class="mb-1"></p>
                <p x-text="currentNote.verse ? currentNote.verse.reference : ''" class="text-sm font-semibold text-right"></p>
            </div>

            <!-- Placeholder when no verse is selected -->
            <div class="mb-4 p-3 bg-gray-50 rounded border-l-4 border-gray-300" x-show="currentNote.verse === null">
                <p class="text-gray-500 italic">No verse selected</p>
            </div>

            <!-- Note title -->
            <div class="mb-4">
                <label for="note-title" class="block text-sm font-medium text-gray-700 mb-1">Title (Optional)</label>
                <input
                    type="text"
                    id="note-title"
                    name="note-title"
                    x-model="currentNote.title"
                    placeholder="Give your note a title"
                    class="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary-custom"
                    autocomplete="off"
                >
            </div>

            <!-- Note content -->
            <div class="mb-4">
                <label for="note-content" class="block text-sm font-medium text-gray-700 mb-1">Your Thoughts</label>
                <textarea
                    id="note-content"
                    name="note-content"
                    x-model="currentNote.text"
                    rows="6"
                    placeholder="Write your reflections on this verse..."
                    class="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-primary-custom"
                    autocomplete="off"
                ></textarea>
            </div>

            <!-- Media options -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Add Media</label>
                <div class="flex space-x-2">
                    <!-- Image upload button -->
                    <button
                        type="button"
                        @click="openImageUpload()"
                        class="flex items-center px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                        <i class="fas fa-image mr-2"></i> Add Image
                    </button>

                    <!-- Video embed button -->
                    <button
                        type="button"
                        @click="showVideoEmbedModal = true; console.log('Video button clicked, modal state:', showVideoEmbedModal);"
                        class="flex items-center px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                    >
                        <i class="fas fa-video mr-2"></i> Add Video
                    </button>
                </div>

                <!-- Hidden file input for image upload -->
                <input
                    type="file"
                    id="image-upload"
                    accept="image/*"
                    class="hidden"
                    @change="handleImageUpload"
                >

                <!-- Preview area for media -->
                <div class="mt-3" x-show="currentNote.media && currentNote.media.length > 0">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Media Preview</h4>
                    <div class="grid grid-cols-1 gap-2">
                        <template x-for="(item, index) in currentNote.media" :key="index">
                            <div class="relative bg-gray-100 p-2 rounded">
                                <!-- Image preview -->
                                <div x-show="item.type === 'image'" class="relative">
                                    <img :src="item.url" class="max-w-full h-auto rounded" style="max-height: 200px;">
                                </div>

                                <!-- Video preview -->
                                <div x-show="item.type === 'video'" class="relative">
                                    <div class="bg-black rounded flex items-center justify-center" style="height: 150px;">
                                        <i class="fas fa-play-circle text-white text-4xl"></i>
                                        <span class="text-white ml-2" x-text="item.provider + ' Video'"></span>
                                    </div>
                                </div>

                                <!-- Remove button -->
                                <button
                                    @click="removeMedia(index)"
                                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600"
                                    title="Remove"
                                >
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="flex justify-between items-center">
                <button
                    @click="addToPost()"
                    class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 flex items-center"
                >
                    <i class="fas fa-file-alt mr-2"></i> Add to Post
                </button>

                <div class="flex space-x-2">
                    <button
                        @click="cancelNote()"
                        class="px-4 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-100"
                    >
                        Cancel
                    </button>
                    <button
                        @click="saveNote()"
                        class="px-4 py-2 bg-primary-custom text-white rounded hover:bg-primary-dark"
                    >
                        Save Note
                    </button>
                </div>
            </div>
        </div>

        <!-- Notes List -->
        <div x-show="notes.length > 0 && !showNoteEditor" class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <template x-for="(note, index) in notes" :key="index">
                <div class="note-card bg-black p-4 rounded-lg shadow hover:shadow-md transition-shadow duration-200" style="background-color: white !important;">
                    <div class="flex justify-between items-start">
                        <h3 class="font-bold text-lg" x-text="note.title || (note.verse ? 'Note on ' + note.verse.reference : 'Untitled Note')"></h3>
                        <div class="flex space-x-1">
                            <button
                                @click="editNote(index)"
                                class="text-gray-500 hover:text-primary-custom"
                                title="Edit note"
                            >
                                <i class="fas fa-edit"></i>
                            </button>
                            <button
                                @click="deleteNote(index)"
                                class="text-gray-500 hover:text-red-500"
                                title="Delete note"
                            >
                                <i class="fas fa-trash-alt"></i>
                            </button>

                            <button
                                @click="addNoteToPost(index)"
                                class="text-gray-500 hover:text-blue-500"
                                title="Add to post"
                            >
                                <i class="fas fa-file-alt"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-sm text-gray-500 mb-2" x-text="note.verse ? note.verse.reference : 'No reference'"></div>
                    <div class="p-3 bg-gray-50 rounded border-l-2 border-gray-300 mb-3 text-sm italic" x-show="note.verse">
                        <p x-text="note.verse ? note.verse.text : ''"></p>
                    </div>
                    <div class="p-3 bg-gray-50 rounded border-l-2 border-gray-300 mb-3 text-sm italic" x-show="!note.verse">
                        <p class="text-gray-400">No verse selected</p>
                    </div>
                    <p class="text-gray-700" x-text="note.text"></p>

                    <!-- Media previews -->
                    <div class="mt-3" x-show="note.media && note.media.length > 0">
                        <div class="grid grid-cols-1 gap-2">
                            <template x-for="(item, mediaIndex) in note.media" :key="mediaIndex">
                                <!-- Image preview -->
                                <div x-show="item.type === 'image'" class="relative">
                                    <img :src="item.url" class="max-w-full h-auto rounded" style="max-height: 150px;">
                                </div>

                                <!-- Video preview -->
                                <div x-show="item.type === 'video'" class="relative">
                                    <div class="bg-black rounded flex items-center justify-center p-2" style="height: 80px;">
                                        <i class="fas fa-play-circle text-white text-2xl mr-2"></i>
                                        <span class="text-white text-sm" x-text="item.provider + ' Video'"></span>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Bible Study Resources -->
    <div class="resources-container">
        <h2 class="text-2xl font-bold mb-4">Bible Study Resources</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <a href="https://biblehub.com" target="_blank" class="resource-link">
                <i class="fas fa-book-open text-2xl text-primary-custom mr-4"></i>
                <div>
                    <div class="font-bold">BibleHub.com</div>
                    <div class="text-sm text-gray-600">Bible study tools, commentaries, concordances, and more</div>
                </div>
            </a>
            <a href="https://www.blueletterbible.org" target="_blank" class="resource-link">
                <i class="fas fa-search text-2xl text-primary-custom mr-4"></i>
                <div>
                    <div class="font-bold">Blue Letter Bible</div>
                    <div class="text-sm text-gray-600">In-depth study tools and original language resources</div>
                </div>
            </a>
            <a href="https://www.biblegateway.com" target="_blank" class="resource-link">
                <i class="fas fa-globe text-2xl text-primary-custom mr-4"></i>
                <div>
                    <div class="font-bold">Bible Gateway</div>
                    <div class="text-sm text-gray-600">Read the Bible in multiple translations and languages</div>
                </div>
            </a>
            <a href="https://www.biblestudytools.com" target="_blank" class="resource-link">
                <i class="fas fa-tools text-2xl text-primary-custom mr-4"></i>
                <div>
                    <div class="font-bold">Bible Study Tools</div>
                    <div class="text-sm text-gray-600">Commentaries, concordances, dictionaries, and more</div>
                </div>
            </a>
        </div>
    </div>
</div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Debug script to check for errors
console.log('Bible Study page loaded');
window.addEventListener('error', function(e) {
    console.error('Global error caught:', e.error);
});

// Make sure Alpine.js is loaded
document.addEventListener('alpine:init', () => {
    console.log('Alpine.js initialized');
});

// Fallback if Alpine.js is not loaded
if (typeof Alpine === 'undefined') {
    console.error('Alpine.js is not loaded! Attempting to load it...');
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js';
    script.defer = true;
    document.head.appendChild(script);
}

// Define the Bible Study component
function bibleStudy() {
    return {
        searchQuery: '',
        searchResults: [],
        isLoading: false,
        notes: [],
        showNoteEditor: false,
        showFeaturedVerses: false, // New state variable to track featured verses visibility
        showVideoEmbedModal: false, // For video embed modal
        videoUrl: '', // For video embed URL
        currentNote: {
            verse: null,
            text: '',
            title: '',
            isEditing: false,
            media: [] // Array to store media (images and videos)
        },
        // Flag modal variables
        showFlagModal: false,
        flagReason: '',
        flagOtherReason: '',
        flagContentId: null,
        flagContentType: '',
        // Initialize with a default verse to ensure something is displayed
        dailyVerse: {
            reference: 'John 3:16',
            text: 'For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.',
            translation: 'ESV'
        },
        featuredVerses: [
            {
                reference: 'Philippians 4:13',
                text: 'I can do all things through him who strengthens me.',
                translation: 'ESV'
            },
            {
                reference: 'Jeremiah 29:11',
                text: 'For I know the plans I have for you, declares the LORD, plans for welfare and not for evil, to give you a future and a hope.',
                translation: 'ESV'
            },
            {
                reference: 'Romans 8:28',
                text: 'And we know that for those who love God all things work together for good, for those who are called according to his purpose.',
                translation: 'ESV'
            },
            {
                reference: 'Psalm 23:1',
                text: 'The LORD is my shepherd; I shall not want.',
                translation: 'ESV'
            },
            {
                reference: 'Isaiah 41:10',
                text: 'Fear not, for I am with you; be not dismayed, for I am your God; I will strengthen you, I will help you, I will uphold you with my righteous right hand.',
                translation: 'ESV'
            },
            {
                reference: 'Proverbs 3:5-6',
                text: 'Trust in the LORD with all your heart, and do not lean on your own understanding. In all your ways acknowledge him, and he will make straight your paths.',
                translation: 'ESV'
            }
        ],

        init() {
            console.log('Initializing Bible Study component');
            // Set default values to prevent undefined errors
            this.showVideoEmbedModal = false;
            this.videoUrl = '';
            this.showFlagModal = false;

            // Load data
            this.getDailyVerse();
            this.loadNotes();
        },

        async searchBible() {
            if (!this.searchQuery.trim()) return;

            try {
                const response = await fetch(`/api/bible/search?query=${encodeURIComponent(this.searchQuery.trim())}`);
                if (response.ok) {
                    const data = await response.json();
                    this.searchResults = data;
                } else {
                    console.error('Error searching Bible:', response.statusText);
                    this.searchResults = [];
                }
            } catch (error) {
                console.error('Error searching Bible:', error);
                this.searchResults = [];
            }
        },

        async getDailyVerse() {
            // Define a set of daily verses to use as fallback
            const dailyVerses = [
                {
                    reference: 'John 3:16',
                    text: 'For God so loved the world, that he gave his only Son, that whoever believes in him should not perish but have eternal life.',
                    translation: 'ESV'
                },
                {
                    reference: 'Philippians 4:13',
                    text: 'I can do all things through him who strengthens me.',
                    translation: 'ESV'
                },
                {
                    reference: 'Jeremiah 29:11',
                    text: 'For I know the plans I have for you, declares the LORD, plans for welfare and not for evil, to give you a future and a hope.',
                    translation: 'ESV'
                },
                {
                    reference: 'Romans 8:28',
                    text: 'And we know that for those who love God all things work together for good, for those who are called according to his purpose.',
                    translation: 'ESV'
                },
                {
                    reference: 'Psalm 23:1',
                    text: 'The LORD is my shepherd; I shall not want.',
                    translation: 'ESV'
                }
            ];

            // Get a random verse for today
            const today = new Date();
            const seed = today.getFullYear() * 10000 + (today.getMonth() + 1) * 100 + today.getDate();
            const randomIndex = seed % dailyVerses.length;

            // Set the daily verse immediately to ensure something is displayed
            this.dailyVerse = dailyVerses[randomIndex];
            console.log('Initial daily verse set:', this.dailyVerse);

            // Try to fetch from API, but keep the fallback if it fails
            try {
                const response = await fetch(`/api/bible/verse/${encodeURIComponent(this.dailyVerse.reference)}`);
                if (response.ok) {
                    const data = await response.json();
                    if (data && data.text && data.reference) {
                        this.dailyVerse = data;
                        console.log('API daily verse loaded:', this.dailyVerse);
                    }
                }
            } catch (error) {
                console.error('Error fetching daily verse:', error);
                // We already set the fallback verse above, so no need to do anything here
            }
        },

        // Note-taking functions
        loadNotes() {
            this.notes = JSON.parse(localStorage.getItem('bibleNotes') || '[]');
        },

        addNote(verse) {
            this.currentNote = {
                verse: verse,
                text: '',
                title: '',
                isEditing: false
            };
            this.showNoteEditor = true;
        },

        editNote(index) {
            // Make a deep copy of the note to edit
            const noteToEdit = JSON.parse(JSON.stringify(this.notes[index]));
            noteToEdit.isEditing = true;

            // Ensure media array exists
            if (!noteToEdit.media) {
                noteToEdit.media = [];
            }

            this.currentNote = noteToEdit;
            this.showNoteEditor = true;
        },

        deleteNote(index) {
            if (confirm('Are you sure you want to delete this note?')) {
                this.notes.splice(index, 1);
                localStorage.setItem('bibleNotes', JSON.stringify(this.notes));
            }
        },

        saveNote() {
            if (!this.currentNote.text.trim() && (!this.currentNote.media || this.currentNote.media.length === 0)) {
                alert('Please enter some thoughts or add media to your note.');
                return;
            }

            // Prepare media for storage
            let mediaToStore = [];
            if (this.currentNote.media && this.currentNote.media.length > 0) {
                // Process each media item
                this.currentNote.media.forEach(media => {
                    if (media.type === 'image') {
                        // For images, we need to convert blob URLs to data URLs for storage
                        if (media.url.startsWith('blob:')) {
                            try {
                                const img = new Image();
                                img.src = media.url;
                                const canvas = document.createElement('canvas');
                                canvas.width = img.width || 300;
                                canvas.height = img.height || 200;
                                const ctx = canvas.getContext('2d');
                                ctx.drawImage(img, 0, 0);
                                const dataUrl = canvas.toDataURL('image/jpeg', 0.7); // Compress to save space

                                mediaToStore.push({
                                    type: 'image',
                                    url: dataUrl
                                });
                            } catch (e) {
                                console.error('Error converting image:', e);
                                // Skip this image if there's an error
                            }
                        } else {
                            // Already a data URL or regular URL
                            mediaToStore.push({
                                type: 'image',
                                url: media.url
                            });
                        }
                    } else if (media.type === 'video') {
                        // For videos, just store the provider and ID
                        mediaToStore.push({
                            type: 'video',
                            provider: media.provider,
                            videoId: media.videoId,
                            url: media.url
                        });
                    }
                });
            }

            // Create a note object with media
            const noteToSave = {
                ...this.currentNote,
                isEditing: false,
                media: mediaToStore
            };

            if (this.currentNote.isEditing) {
                // Find and update existing note
                const index = this.notes.findIndex(note => {
                    // Handle case where verse might be null
                    if (!note.verse || !this.currentNote.verse) {
                        return note.title === this.currentNote.title;
                    }
                    return note.verse.reference === this.currentNote.verse.reference &&
                           note.title === this.currentNote.title;
                });

                if (index !== -1) {
                    this.notes[index] = noteToSave;
                } else {
                    this.notes.push(noteToSave);
                }
            } else {
                // Add new note
                this.notes.push(noteToSave);
            }

            // Save to localStorage
            localStorage.setItem('bibleNotes', JSON.stringify(this.notes));

            // Reset and hide editor
            this.showNoteEditor = false;
            this.currentNote = { verse: null, text: '', title: '', isEditing: false, media: [] };
        },

        cancelNote() {
            // Clean up any blob URLs to prevent memory leaks
            if (this.currentNote.media && this.currentNote.media.length > 0) {
                this.currentNote.media.forEach(media => {
                    if (media.type === 'image' && media.url.startsWith('blob:')) {
                        URL.revokeObjectURL(media.url);
                    }
                });
            }

            this.showNoteEditor = false;
            this.currentNote = { verse: null, text: '', title: '', isEditing: false, media: [] };
        },

        // Media handling functions
        openImageUpload() {
            document.getElementById('image-upload').click();
        },

        handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // Check file type
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file.');
                return;
            }

            // Check file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Image size should be less than 5MB.');
                return;
            }

            // Create a URL for the image
            const imageUrl = URL.createObjectURL(file);

            // Add to media array
            if (!this.currentNote.media) {
                this.currentNote.media = [];
            }

            this.currentNote.media.push({
                type: 'image',
                url: imageUrl,
                file: file // Store the file for later upload
            });

            // Reset the file input
            event.target.value = '';
        },

        addVideo() {
            console.log('Adding video, modal state:', this.showVideoEmbedModal);
            const url = this.videoUrl.trim();
            if (!url) {
                alert('Please enter a video URL.');
                return;
            }

            try {
                // Parse YouTube URL
                let youtubeMatch = url.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/ ]{11})/);
                if (youtubeMatch) {
                    const videoId = youtubeMatch[1];
                    if (!this.currentNote.media) {
                        this.currentNote.media = [];
                    }

                    this.currentNote.media.push({
                        type: 'video',
                        provider: 'YouTube',
                        videoId: videoId,
                        url: `https://www.youtube.com/embed/${videoId}`
                    });

                    this.videoUrl = '';
                    this.showVideoEmbedModal = false;
                    console.log('YouTube video added, media:', this.currentNote.media);
                    return;
                }

                // Parse Vimeo URL
                let vimeoMatch = url.match(/(?:vimeo\.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|)(\d+)(?:|\/\?))/);
                if (vimeoMatch) {
                    const videoId = vimeoMatch[2];
                    if (!this.currentNote.media) {
                        this.currentNote.media = [];
                    }

                    this.currentNote.media.push({
                        type: 'video',
                        provider: 'Vimeo',
                        videoId: videoId,
                        url: `https://player.vimeo.com/video/${videoId}`
                    });

                    this.videoUrl = '';
                    this.showVideoEmbedModal = false;
                    console.log('Vimeo video added, media:', this.currentNote.media);
                    return;
                }

                alert('Please enter a valid YouTube or Vimeo URL.');
            } catch (error) {
                console.error('Error adding video:', error);
                alert('An error occurred while adding the video. Please try again.');
            }
        },

        removeMedia(index) {
            if (this.currentNote.media && this.currentNote.media.length > index) {
                // If it's an image with a blob URL, revoke it to free memory
                const media = this.currentNote.media[index];
                if (media.type === 'image' && media.url.startsWith('blob:')) {
                    URL.revokeObjectURL(media.url);
                }

                // Remove the item from the array
                this.currentNote.media.splice(index, 1);
            }
        },

        addToPost() {
            // Check if there's content to add
            if (!this.currentNote.text.trim() && (!this.currentNote.media || this.currentNote.media.length === 0)) {
                alert('Please enter some thoughts or add media before adding to a post.');
                return;
            }

            // Format the note content for a blog post
            let formattedContent = '';

            // Add title if available
            if (this.currentNote.title) {
                formattedContent += `<h3>${this.currentNote.title}</h3>\n`;
            }

            // Add verse if available
            if (this.currentNote.verse) {
                formattedContent += `<blockquote class="p-4 border-l-4 border-primary-custom bg-gray-50 mb-4">\n`;
                formattedContent += `  <p class="mb-2">${this.currentNote.verse.text}</p>\n`;
                formattedContent += `  <footer class="text-right font-bold">${this.currentNote.verse.reference}</footer>\n`;
                formattedContent += `</blockquote>\n`;
            }

            // Add note text
            formattedContent += `<p>${this.currentNote.text}</p>\n`;

            // Add media content
            if (this.currentNote.media && this.currentNote.media.length > 0) {
                this.currentNote.media.forEach(media => {
                    if (media.type === 'image') {
                        // For images, we'll need to convert the blob URL to a data URL
                        const img = new Image();
                        img.src = media.url;
                        const canvas = document.createElement('canvas');
                        canvas.width = img.width;
                        canvas.height = img.height;
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);
                        const dataUrl = canvas.toDataURL('image/jpeg');

                        formattedContent += `<p><img src="${dataUrl}" alt="${this.currentNote.title || 'Bible Study Note'}" class="max-w-full h-auto rounded"></p>\n`;
                    } else if (media.type === 'video') {
                        // For videos, add an iframe
                        formattedContent += `<div class="video-container" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; max-width: 100%; margin-bottom: 1rem;">\n`;
                        formattedContent += `  <iframe src="${media.url}" frameborder="0" allowfullscreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></iframe>\n`;
                        formattedContent += `</div>\n`;
                    }
                });
            }

            // Save the note first
            this.saveNote();

            // Redirect to create post page with the content
            localStorage.setItem('pendingPostContent', formattedContent);
            window.location.href = '/create-post?from=bible_study';
        },

        addNoteToPost(index) {
            const note = this.notes[index];
            if (!note) return;

            // Format the note content for a blog post
            let formattedContent = '';

            // Add title if available
            if (note.title) {
                formattedContent += `<h3>${note.title}</h3>\n`;
            }

            // Add verse if available
            if (note.verse) {
                formattedContent += `<blockquote class="p-4 border-l-4 border-primary-custom bg-gray-50 mb-4">\n`;
                formattedContent += `  <p class="mb-2">${note.verse.text}</p>\n`;
                formattedContent += `  <footer class="text-right font-bold">${note.verse.reference}</footer>\n`;
                formattedContent += `</blockquote>\n`;
            }

            // Add note text
            formattedContent += `<p>${note.text}</p>\n`;

            // Add media content if available
            if (note.media && note.media.length > 0) {
                note.media.forEach(media => {
                    if (media.type === 'image') {
                        // For images, use the stored URL
                        formattedContent += `<p><img src="${media.url}" alt="${note.title || 'Bible Study Note'}" class="max-w-full h-auto rounded"></p>\n`;
                    } else if (media.type === 'video') {
                        // For videos, add an iframe
                        formattedContent += `<div class="video-container" style="position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; max-width: 100%; margin-bottom: 1rem;">\n`;
                        formattedContent += `  <iframe src="${media.url}" frameborder="0" allowfullscreen style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;"></iframe>\n`;
                        formattedContent += `</div>\n`;
                    }
                });
            }

            // Redirect to create post page with the content
            localStorage.setItem('pendingPostContent', formattedContent);
            window.location.href = '/create-post?from=bible_study';
        },

        copyToClipboard(content) {
            // Create a temporary textarea element
            const textarea = document.createElement('textarea');
            textarea.value = content.replace(/<[^>]*>/g, ''); // Strip HTML tags
            document.body.appendChild(textarea);
            textarea.select();

            try {
                document.execCommand('copy');
                alert('Copied to clipboard!');
            } catch (err) {
                console.error('Failed to copy text: ', err);
            } finally {
                document.body.removeChild(textarea);
            }
        }
    };
}
</script>

<!-- Share Modal Script removed -->

<!-- Include Flag Content Script -->
<script src="/static/js/bible-flag.js"></script>
{% endblock %}
