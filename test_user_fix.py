#!/usr/bin/env python3
"""
Test script to verify the User model duplicate issue is fixed.
"""

def test_user_import():
    """Test that User model can be imported without conflicts."""
    try:
        print("Testing User model import...")
        from app.models import User
        print("✓ User model imported successfully")
        print(f"✓ User table name: {User.__tablename__}")
        print(f"✓ User has {len(User.__table__.columns)} columns")
        return True
    except Exception as e:
        print(f"✗ Error importing User model: {e}")
        return False

def test_database_creation():
    """Test that database tables can be created."""
    try:
        print("\nTesting database table creation...")
        from app.database import Base, engine
        import app.models  # Import to register models
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        print("✓ Database tables created successfully")
        print(f"✓ Available tables: {list(Base.metadata.tables.keys())}")
        return True
    except Exception as e:
        print(f"✗ Error creating database tables: {e}")
        return False

def test_user_creation():
    """Test that a User can be created."""
    try:
        print("\nTesting User creation...")
        from app.models import User
        from app.database import SessionLocal
        from app.auth import get_password_hash
        
        db = SessionLocal()
        
        # Check if test user exists and delete if so
        existing_user = db.query(User).filter(User.username == 'test_fix_user').first()
        if existing_user:
            db.delete(existing_user)
            db.commit()
        
        # Create new user
        user = User(
            username='test_fix_user',
            email='<EMAIL>',
            hashed_password=get_password_hash('testpassword'),
            role='user'
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"✓ User created successfully: ID={user.id}, Username={user.username}")
        
        # Clean up
        db.delete(user)
        db.commit()
        db.close()
        return True
    except Exception as e:
        print(f"✗ Error creating user: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Testing User Model Duplicate Fix")
    print("=" * 50)
    
    success = True
    success &= test_user_import()
    success &= test_database_creation()
    success &= test_user_creation()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL TESTS PASSED! User model duplicate issue is FIXED!")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print("=" * 50)
