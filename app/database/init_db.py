"""Database initialization for the DavarTruth application."""
from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from app.database import SessionLocal, Base, engine
from app.models import User, BlogPost, PrayerRequest, Event
from datetime import datetime, timedelta

def init_database():
    """Initialize the database with some sample data."""
    # Create all tables
    Base.metadata.create_all(bind=engine)
    
    db = SessionLocal()
    
    try:
        # Create sample users
        admin = User(
            email="<EMAIL>",
            username="admin",
            hashed_password="$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewETkOhzcpqJ6ZZi",  # Change this!
            is_superuser=True
        )
        db.add(admin)
        
        # Create sample posts
        sample_post = BlogPost(
            title="Welcome to DavarTruth",
            content="This is our first blog post. Welcome to our community!",
            user_id=1,
            is_published=True
        )
        db.add(sample_post)

        # Create sample prayer requests
        prayer = PrayerRequest(
            title="Community Prayer",
            content="Please pray for our community.",
            user_id=1,
            is_anonymous=False
        )
        db.add(prayer)

        # Create sample church events
        event = Event(
            title="Sunday Service",
            description="Join us for our weekly service",
            event_date=datetime.utcnow() + timedelta(days=7),
            location="Main Sanctuary",
            organizer_id=1
        )
        db.add(event)
        
        db.commit()
        print("Database initialized successfully with sample data!")
        
    except Exception as e:
        print(f"Error initializing database: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
