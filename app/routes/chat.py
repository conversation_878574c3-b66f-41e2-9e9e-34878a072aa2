import logging
from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request, HTTPException, status, Body
from fastapi.responses import RedirectResponse
from fastapi.templating import Jin<PERSON>2Templates
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from typing import Dict, Any, List
from ..database import get_async_db_dependency
from ..models import Chat<PERSON>oom, ChatMessage, User, MessageStatus, PrayerRequest, Prayer, BibleStudyPost, BibleStudyComment, GeneralPost, GeneralComment
from ..dependencies import get_current_user

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/chat")
async def get_chat(
    request: Request,
    db: AsyncSession = Depends(get_async_db_dependency)
):
    """Main chat interface using WhatsApp style"""
    try:
        # Get current user
        user = getattr(request.state, 'user', None)
        logger.info(f"User from request.state: {user}")
        is_authenticated = user is not None

        # Initialize with safe defaults
        rooms = []
        messages = []

        try:
            # Get available chat rooms
            logger.info("Querying chat rooms")
            rooms_query = select(ChatRoom)
            rooms_result = await db.execute(rooms_query)
            rooms = rooms_result.scalars().all()
            logger.info(f"Found {len(rooms)} chat rooms")

            if not rooms:
                # Create default room if none exist
                logger.info("No rooms found, creating default room")
                default_room = ChatRoom(
                    name="General",
                    description="General discussion room"
                )
                db.add(default_room)
                await db.commit()
                rooms = [default_room]
                logger.info("Default room created successfully")
        except Exception as e:
            logger.error(f"Error getting chat rooms: {e}")
            logger.exception("Full traceback for chat rooms error:")
            # Continue with empty rooms list

        try:
            # Get recent messages
            logger.info("Querying recent chat messages")
            messages_query = select(ChatMessage).order_by(ChatMessage.created_at.desc()).limit(50)
            messages_result = await db.execute(messages_query)
            messages = messages_result.scalars().all()
            logger.info(f"Found {len(messages)} chat messages")
        except Exception as e:
            logger.error(f"Error getting chat messages: {e}")
            logger.exception("Full traceback for chat messages error:")
            # Continue with empty messages list

        logger.info("Rendering chat_whatsapp.html template")
        try:
            return request.app.state.templates.TemplateResponse(
                "chat_whatsapp.html",
                {
                    "request": request,
                    "user": user,
                    "is_authenticated": is_authenticated,
                    "rooms": rooms,
                    "messages": messages
                }
            )
        except Exception as e:
            logger.error(f"Error rendering chat template: {e}")
            logger.exception("Full traceback for template rendering error:")
            raise
    except Exception as e:
        logger.error(f"Unhandled error in get_chat: {e}")
        logger.exception("Full traceback for unhandled error:")

        try:
            logger.info("Rendering error.html template")
            return request.app.state.templates.TemplateResponse(
                "error.html",
                {
                    "request": request,
                    "error_message": f"There was an error loading the chat: {str(e)}",
                    "user": getattr(request.state, 'user', None),
                    "is_authenticated": getattr(request.state, 'user', None) is not None
                },
                status_code=500
            )
        except Exception as template_error:
            logger.error(f"Error rendering error.html template: {template_error}")
            logger.exception("Full traceback for error template rendering:")

            # Last resort: return a plain text response
            from fastapi.responses import PlainTextResponse
            return PlainTextResponse(
                content=f"Server error: {str(e)}. Template error: {str(template_error)}",
                status_code=500
            )

@router.get("/chat/{room_id}")
async def chat_room_page(room_id: int, request: Request, db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        # Initialize with safe defaults
        room = None
        rooms = []
        messages = []

        # Get user info
        user = request.state.user
        is_authenticated = user is not None

        try:
            # Get room info
            query = select(ChatRoom).where(ChatRoom.id == room_id)
            result = await db.execute(query)
            room = result.scalars().first()

            if not room:
                return RedirectResponse(url="/chat", status_code=status.HTTP_302_FOUND)

            # If room is private and user is not authenticated, redirect to login
            if getattr(room, 'is_private', False) and not is_authenticated:
                return RedirectResponse(url=f"/login?next=/chat/{room_id}", status_code=status.HTTP_302_FOUND)
        except Exception as e:
            logger.error(f"Error getting chat room {room_id}: {e}")
            return RedirectResponse(url="/chat", status_code=status.HTTP_302_FOUND)

        try:
            # Get available chat rooms for sidebar
            rooms_query = select(ChatRoom).order_by(ChatRoom.name)
            rooms_result = await db.execute(rooms_query)
            rooms = rooms_result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting chat rooms list: {e}")
            # Continue with empty rooms list

        try:
            # Get recent messages for this room
            messages_query = select(ChatMessage).where(
                (ChatMessage.room_id == room_id) &
                (ChatMessage.status != MessageStatus.DELETED)
            ).order_by(ChatMessage.created_at.desc()).limit(50)
            messages_result = await db.execute(messages_query)
            messages = messages_result.scalars().all()

            # Reverse messages to show oldest first
            messages = list(reversed(messages))
        except Exception as e:
            logger.error(f"Error getting chat messages for room {room_id}: {e}")
            # Continue with empty messages list

        return request.app.state.templates.TemplateResponse(
            "chat_whatsapp.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": is_authenticated,
                "room": room,
                "rooms": rooms,
                "messages": messages
            }
        )
    except Exception as e:
        logger.error(f"Unhandled error in chat_room_page: {e}")
        # Return a simple error page instead of 500
        return request.app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the chat room. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.get("/chat/messages")
async def get_messages(db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        query = select(ChatMessage).order_by(ChatMessage.created_at.desc()).limit(50)
        result = await db.execute(query)
        messages = result.scalars().all()

        # Convert to list of dictionaries
        message_list = []
        for message in messages:
            try:
                message_data = {
                    "id": message.id,
                    "content": message.content,
                    "username": message.username,
                    "created_at": message.created_at.isoformat() if message.created_at else None,
                    "user_id": message.user_id,
                    "room_id": message.room_id
                }
                message_list.append(message_data)
            except Exception as e:
                print(f"Error processing message {getattr(message, 'id', 'unknown')}: {e}")
                # Skip this message and continue

        return message_list
    except Exception as e:
        print(f"Error getting messages: {e}")
        # Return empty list on error
        return []

@router.get("/chat/rooms")
async def get_chat_rooms(db: AsyncSession = Depends(get_async_db_dependency)):
    """Get all available chat rooms"""
    try:
        # Get available chat rooms
        rooms_query = select(ChatRoom).order_by(ChatRoom.name)
        rooms_result = await db.execute(rooms_query)
        rooms = rooms_result.scalars().all()

        # Convert to list of dictionaries
        room_list = []
        for room in rooms:
            room_data = {
                "id": str(room.id),
                "name": room.name,
                "description": room.description
            }
            room_list.append(room_data)

        return room_list
    except Exception as e:
        logger.error(f"Error getting chat rooms: {e}")
        # Return empty list on error
        return []

@router.get("/chat/messages/{room_id}")
async def get_room_messages(room_id: int, db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        # First check if the room exists
        room_query = select(ChatRoom).where(ChatRoom.id == room_id)
        room_result = await db.execute(room_query)
        room = room_result.scalars().first()

        if not room:
            # Return empty list if room doesn't exist
            return []

        # Get messages for this room with user information
        query = select(ChatMessage, User).outerjoin(
            User, ChatMessage.user_id == User.id
        ).where(ChatMessage.room_id == room_id).order_by(ChatMessage.created_at.desc()).limit(50)
        result = await db.execute(query)
        message_users = result.fetchall()

        # Convert to list of dictionaries
        message_list = []
        for message_user in message_users:
            message, user = message_user
            message_data = {
                "id": message.id,
                "content": message.content,
                "username": message.username,
                "created_at": message.created_at.isoformat(),
                "user_id": message.user_id,
                "room_id": message.room_id
            }

            # Add user data if available
            if user:
                message_data["user"] = {
                    "id": user.id,
                    "username": user.username,
                    "profile_picture": user.profile_picture
                }

            message_list.append(message_data)

        return message_list
    except Exception as e:
        print(f"Error getting messages for room {room_id}: {e}")
        # Return empty list on error
        return []

@router.get("/prayer-requests")
async def prayer_requests_page(request: Request, db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        # Get user info
        user = request.state.user
        is_authenticated = user is not None
        prayer_requests = []

        try:
            # Get prayer requests
            if is_authenticated:
                # If user is authenticated, show public requests and their own private requests
                query = select(PrayerRequest).where(
                    (PrayerRequest.is_private == False) |
                    (PrayerRequest.user_id == user.id)
                ).order_by(PrayerRequest.created_at.desc())
            else:
                # If not authenticated, only show public requests
                query = select(PrayerRequest).where(
                    PrayerRequest.is_private == False
                ).order_by(PrayerRequest.created_at.desc())

            result = await db.execute(query)
            prayer_requests = result.scalars().all()
        except Exception as e:
            print(f"Error getting prayer requests: {e}")
            # Continue with empty prayer_requests list

        return request.app.state.templates.TemplateResponse(
            "prayer_requests.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": is_authenticated,
                "prayer_requests": prayer_requests
            }
        )
    except Exception as e:
        print(f"Unhandled error in prayer_requests_page: {e}")
        # Return a simple error page instead of 500
        return request.app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the prayer requests page. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.post("/prayer-requests")
async def create_prayer_request(
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get request data
    title = data.get("title")
    content = data.get("content")
    is_anonymous = data.get("is_anonymous", False)
    is_private = data.get("is_private", False)

    # Validate input
    if not title or not content:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title and content are required")

    # Create prayer request
    prayer_request = PrayerRequest(
        user_id=user.id,
        title=title,
        content=content,
        is_anonymous=is_anonymous,
        is_private=is_private
    )

    db.add(prayer_request)
    await db.commit()
    await db.refresh(prayer_request)

    return {"success": True, "id": prayer_request.id}

@router.post("/prayer-requests/{request_id}/pray")
async def pray_for_request(
    request_id: int,
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get prayer request
    query = select(PrayerRequest).where(PrayerRequest.id == request_id)
    result = await db.execute(query)
    prayer_request = result.scalars().first()

    if not prayer_request:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Prayer request not found")

    # Create prayer
    prayer = Prayer(
        prayer_request_id=prayer_request.id,
        user_id=user.id,
        content=data.get("content")
    )

    db.add(prayer)
    await db.commit()

    return {"success": True}

# WhatsApp-style chat routes have been integrated into the main chat routes

@router.get("/bible-study")
async def bible_study_page(request: Request, db: AsyncSession = Depends(get_async_db_dependency)):
    """Bible study page with verse lookup and search"""
    try:
        # Get user info
        user = request.state.user
        is_authenticated = user is not None

        return request.app.state.templates.TemplateResponse(
            "bible_study.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": is_authenticated
            }
        )
    except Exception as e:
        print(f"Unhandled error in bible_study_page: {e}")
        # Return a simple error page instead of 500
        return request.app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the Bible study page. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.get("/bible-study-posts")
async def bible_study_posts_page(request: Request, db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        # Get user info
        user = request.state.user
        is_authenticated = user is not None
        posts = []

        try:
            # Get Bible study posts
            if is_authenticated:
                # If user is authenticated, show public posts and their own private posts
                query = select(BibleStudyPost).where(
                    (BibleStudyPost.is_private == False) |
                    (BibleStudyPost.user_id == user.id)
                ).order_by(BibleStudyPost.created_at.desc())
            else:
                # If not authenticated, only show public posts
                query = select(BibleStudyPost).where(
                    BibleStudyPost.is_private == False
                ).order_by(BibleStudyPost.created_at.desc())

            result = await db.execute(query)
            posts = result.scalars().all()
        except Exception as e:
            print(f"Error getting Bible study posts: {e}")
            # Continue with empty posts list

        return request.app.state.templates.TemplateResponse(
            "bible_study_posts.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": is_authenticated,
                "posts": posts
            }
        )
    except Exception as e:
        print(f"Unhandled error in bible_study_posts_page: {e}")
        # Return a simple error page instead of 500
        return request.app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the Bible study posts page. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.post("/bible-study-posts")
async def create_bible_study_post(
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get request data
    title = data.get("title")
    content = data.get("content")
    verse_reference = data.get("verse_reference")
    is_anonymous = data.get("is_anonymous", False)
    is_private = data.get("is_private", False)

    # Validate input
    if not title or not content:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title and content are required")

    # Create Bible study post
    post = BibleStudyPost(
        user_id=user.id,
        title=title,
        content=content,
        verse_reference=verse_reference,
        is_anonymous=is_anonymous,
        is_private=is_private
    )

    db.add(post)
    await db.commit()
    await db.refresh(post)

    return {"success": True, "id": post.id}

@router.post("/bible-study-posts/{post_id}/comment")
async def comment_on_bible_study_post(
    post_id: int,
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get Bible study post
    query = select(BibleStudyPost).where(BibleStudyPost.id == post_id)
    result = await db.execute(query)
    post = result.scalars().first()

    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")

    # Create comment
    comment = BibleStudyComment(
        post_id=post.id,
        user_id=user.id,
        content=data.get("content")
    )

    db.add(comment)
    await db.commit()

    return {"success": True}

@router.get("/general-posts")
async def general_posts_page(request: Request, db: AsyncSession = Depends(get_async_db_dependency)):
    try:
        # Get user info
        user = request.state.user
        is_authenticated = user is not None
        posts = []

        try:
            # Get general posts
            if is_authenticated:
                # If user is authenticated, show public posts and their own private posts
                query = select(GeneralPost).where(
                    (GeneralPost.is_private == False) |
                    (GeneralPost.user_id == user.id)
                ).order_by(GeneralPost.created_at.desc())
            else:
                # If not authenticated, only show public posts
                query = select(GeneralPost).where(
                    GeneralPost.is_private == False
                ).order_by(GeneralPost.created_at.desc())

            result = await db.execute(query)
            posts = result.scalars().all()
        except Exception as e:
            print(f"Error getting general posts: {e}")
            # Continue with empty posts list

        return request.app.state.templates.TemplateResponse(
            "general_posts.html",
            {
                "request": request,
                "user": user,
                "is_authenticated": is_authenticated,
                "posts": posts
            }
        )
    except Exception as e:
        print(f"Unhandled error in general_posts_page: {e}")
        # Return a simple error page instead of 500
        return request.app.state.templates.TemplateResponse(
            "error.html",
            {
                "request": request,
                "error_message": "There was an error loading the general posts page. Please try again later.",
                "user": getattr(request.state, 'user', None),
                "is_authenticated": getattr(request.state, 'user', None) is not None
            }
        )

@router.post("/general-posts")
async def create_general_post(
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get request data
    title = data.get("title")
    content = data.get("content")
    is_anonymous = data.get("is_anonymous", False)
    is_private = data.get("is_private", False)

    # Validate input
    if not title or not content:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Title and content are required")

    # Create general post
    post = GeneralPost(
        user_id=user.id,
        title=title,
        content=content,
        is_anonymous=is_anonymous,
        is_private=is_private
    )

    db.add(post)
    await db.commit()
    await db.refresh(post)

    return {"success": True, "id": post.id}

@router.post("/general-posts/{post_id}/comment")
async def comment_on_general_post(
    post_id: int,
    request: Request,
    data: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Check if user is authenticated
    user = request.state.user
    if not user:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Authentication required")

    # Get general post
    query = select(GeneralPost).where(GeneralPost.id == post_id)
    result = await db.execute(query)
    post = result.scalars().first()

    if not post:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Post not found")

    # Create comment
    comment = GeneralComment(
        post_id=post.id,
        user_id=user.id,
        content=data.get("content")
    )

    db.add(comment)
    await db.commit()

    return {"success": True}


@router.post("/chat/messages")
async def save_message(
    request: Request,
    message: Dict[str, Any] = Body(...),
    db: AsyncSession = Depends(get_async_db_dependency)
):
    # Get user_id if user is logged in
    user_id = None
    if request.state.user:
        user_id = request.state.user.id

    # Create new message
    new_message = ChatMessage(
        content=message.get("content"),
        username=message.get("username"),
        user_id=user_id
    )

    db.add(new_message)
    await db.commit()
    await db.refresh(new_message)

    return {
        "success": True,
        "message": {
            "id": new_message.id,
            "content": new_message.content,
            "username": new_message.username,
            "created_at": new_message.created_at.isoformat(),
            "user_id": new_message.user_id
        }
    }
