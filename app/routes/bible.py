"""
Bible API routes for fetching and searching Bible verses
"""

from fastapi import APIRouter, Depends, Query, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional

from ..database import get_async_db_dependency
from ..services.bible_service import (
    get_verse,
    get_verse_by_keyword,
    search_verses_by_text,
    extract_bible_references,
    get_biblehub_link
)

router = APIRouter()

@router.get("/api/bible/verse/{reference}")
async def get_bible_verse(reference: str, db: AsyncSession = Depends(get_db)):
    """
    Get a Bible verse by reference
    """
    verse = await get_verse(reference, db)
    if not verse:
        raise HTTPException(status_code=404, detail="Verse not found")

    # Add BibleHub link
    verse["biblehub_link"] = get_biblehub_link(verse["reference"])

    return verse

@router.get("/api/bible/search")
async def search_bible(
    query: str = Query(..., description="Keyword or text to search for"),
    db: AsyncSession = Depends(get_db)
):
    """
    Search for Bible verses by keyword or text
    """
    # Check if query is a Bible reference
    references = extract_bible_references(query)
    if references:
        verse = await get_verse(references[0], db)
        if verse:
            # Add BibleHub link
            verse["biblehub_link"] = get_biblehub_link(verse["reference"])
            return [verse]

    # Try keyword search
    verse = await get_verse_by_keyword(query, db)
    if verse:
        # Add BibleHub link
        verse["biblehub_link"] = get_biblehub_link(verse["reference"])
        return [verse]

    # Try text search
    verses = await search_verses_by_text(query, db)

    # Add BibleHub links
    for verse in verses:
        verse["biblehub_link"] = get_biblehub_link(verse["reference"])

    return verses

@router.get("/api/bible/keyword/{keyword}")
async def get_verse_for_keyword(keyword: str, db: AsyncSession = Depends(get_db)):
    """
    Get a Bible verse for a specific keyword
    """
    verse = await get_verse_by_keyword(keyword, db)
    if not verse:
        raise HTTPException(status_code=404, detail="No verse found for this keyword")

    # Add BibleHub link
    verse["biblehub_link"] = get_biblehub_link(verse["reference"])

    return verse

@router.get("/api/bible/books")
async def get_bible_books():
    """
    Get a list of all Bible books
    """
    # Old Testament books
    old_testament = [
        "Genesis", "Exodus", "Leviticus", "Numbers", "Deuteronomy",
        "Joshua", "Judges", "Ruth", "1 Samuel", "2 Samuel",
        "1 Kings", "2 Kings", "1 Chronicles", "2 Chronicles",
        "Ezra", "Nehemiah", "Esther", "Job", "Psalms",
        "Proverbs", "Ecclesiastes", "Song of Solomon", "Isaiah",
        "Jeremiah", "Lamentations", "Ezekiel", "Daniel",
        "Hosea", "Joel", "Amos", "Obadiah", "Jonah",
        "Micah", "Nahum", "Habakkuk", "Zephaniah",
        "Haggai", "Zechariah", "Malachi"
    ]

    # New Testament books
    new_testament = [
        "Matthew", "Mark", "Luke", "John", "Acts",
        "Romans", "1 Corinthians", "2 Corinthians", "Galatians",
        "Ephesians", "Philippians", "Colossians", "1 Thessalonians",
        "2 Thessalonians", "1 Timothy", "2 Timothy", "Titus",
        "Philemon", "Hebrews", "James", "1 Peter", "2 Peter",
        "1 John", "2 John", "3 John", "Jude", "Revelation"
    ]

    return {
        "old_testament": old_testament,
        "new_testament": new_testament
    }

@router.get("/api/bible/verses")
async def get_bible_verses(
    book: str = Query(..., description="Bible book name"),
    chapter: int = Query(..., description="Chapter number"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get all verses for a specific chapter
    """
    # Sample verses for Genesis 1 (for demonstration)
    if book == "Genesis" and chapter == 1:
        verses = [
            "In the beginning God created the heaven and the earth.",
            "And the earth was without form, and void; and darkness was upon the face of the deep. And the Spirit of God moved upon the face of the waters.",
            "And God said, Let there be light: and there was light.",
            "And God saw the light, that it was good: and God divided the light from the darkness.",
            "And God called the light Day, and the darkness he called Night. And the evening and the morning were the first day.",
            "And God said, Let there be a firmament in the midst of the waters, and let it divide the waters from the waters.",
            "And God made the firmament, and divided the waters which were under the firmament from the waters which were above the firmament: and it was so.",
            "And God called the firmament Heaven. And the evening and the morning were the second day.",
            "And God said, Let the waters under the heaven be gathered together unto one place, and let the dry land appear: and it was so.",
            "And God called the dry land Earth; and the gathering together of the waters called he Seas: and God saw that it was good."
        ]
        return {"book": book, "chapter": chapter, "verses": verses}

    # Sample verses for other books/chapters
    return {
        "book": book,
        "chapter": chapter,
        "verses": [
            "This is a sample verse 1 for " + book + " " + str(chapter) + ".",
            "This is a sample verse 2 for " + book + " " + str(chapter) + ".",
            "This is a sample verse 3 for " + book + " " + str(chapter) + "."
        ]
    }