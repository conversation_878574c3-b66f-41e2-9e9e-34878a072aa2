from datetime import datetime
import asyncio
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession
from .models import BlogPost
from .database import get_async_db

async def publish_scheduled_posts():
    """
    Check for scheduled posts that should be published and publish them.
    This function should be called periodically by a scheduler.
    """
    rows_affected = 0

    try:
        # Get a database session using the context manager
        async with get_async_db() as db:
            # Find posts that are scheduled to be published and the scheduled time has passed
            now = datetime.now()

            # Debug the query
            print(f"Checking for scheduled posts at {now}")

            # Use SQLAlchemy Core to build the query
            stmt = update(BlogPost).where(
                (BlogPost.is_scheduled == True) &
                (BlogPost.scheduled_publish_date <= now)
            ).values(is_published=True, is_scheduled=False)

            # Print the query for debugging
            print(f"Scheduled posts query: {str(stmt)}")

            # Execute the update
            result = await db.execute(stmt)

            # Get the number of rows affected
            rows_affected = result.rowcount

            # Commit changes if any posts were updated
            if rows_affected > 0:
                await db.commit()
                print(f"Published {rows_affected} scheduled posts")
            else:
                print("No scheduled posts to publish")

    except Exception as e:
        print(f"Error in publish_scheduled_posts: {e}")
        import traceback
        traceback.print_exc()
        # Set rows_affected to 0 to indicate no posts were published
        rows_affected = 0

    # Return the number of rows affected
    return rows_affected

async def check_scheduled_posts():
    """
    Background task to periodically check for scheduled posts that should be published.
    This function runs indefinitely in the background.
    """
    while True:
        try:
            # Check for scheduled posts
            await publish_scheduled_posts()

            # Wait for 5 minutes before checking again
            await asyncio.sleep(300)  # 300 seconds = 5 minutes
        except Exception as e:
            print(f"Error in check_scheduled_posts background task: {e}")
            import traceback
            traceback.print_exc()

            # Wait a bit before trying again
            await asyncio.sleep(60)  # 60 seconds = 1 minute
